import 'dart:convert';
import 'package:invoicer/core/db/drift/database.dart';

/// Tax calculation result for a single line
class TaxCalculationResult {
  final double baseAmount;
  final double totalAmount;
  final double taxAmount;
  final List<TaxLineResult> taxLines;

  TaxCalculationResult({
    required this.baseAmount,
    required this.totalAmount,
    required this.taxAmount,
    required this.taxLines,
  });
}

/// Individual tax line result
class TaxLineResult {
  final int taxId;
  final String taxName;
  final double baseAmount;
  final double taxAmount;
  final double rate;
  final bool priceInclude;

  TaxLineResult({
    required this.taxId,
    required this.taxName,
    required this.baseAmount,
    required this.taxAmount,
    required this.rate,
    required this.priceInclude,
  });
}

/// Service for calculating taxes following Odoo's logic
class TaxCalculationService {
  final AppDatabase _database;

  TaxCalculationService(this._database);

  /// Calculate taxes for a single invoice line
  Future<TaxCalculationResult> calculateLineTaxes({
    required double priceUnit,
    required double quantity,
    required double discount,
    required List<int> taxIds,
    bool priceIncludesTax = false,
  }) async {
    // Get tax records
    final taxes = await _database.accountTaxDao.getTaxesByIds(taxIds);
    
    // Sort taxes by sequence (important for compound taxes)
    taxes.sort((a, b) => (a.sequence ?? 1).compareTo(b.sequence ?? 1));

    // Calculate base amount (price * quantity - discount)
    double baseAmount = priceUnit * quantity;
    if (discount > 0) {
      baseAmount = baseAmount * (1 - discount / 100);
    }

    // If price includes tax, we need to extract the tax amount
    if (priceIncludesTax) {
      return _calculateTaxesIncluded(baseAmount, taxes);
    } else {
      return _calculateTaxesExcluded(baseAmount, taxes);
    }
  }

  /// Calculate taxes when price excludes tax (standard case)
  Future<TaxCalculationResult> _calculateTaxesExcluded(
    double baseAmount,
    List<AccountTaxTableData> taxes,
  ) async {
    double currentBase = baseAmount;
    double totalTaxAmount = 0.0;
    List<TaxLineResult> taxLines = [];

    for (final tax in taxes) {
      final taxResult = await _calculateSingleTax(tax, currentBase);
      taxLines.add(taxResult);
      totalTaxAmount += taxResult.taxAmount;

      // If this tax affects the base of subsequent taxes, update the base
      if (tax.include_base_amount == true) {
        currentBase += taxResult.taxAmount;
      }
    }

    return TaxCalculationResult(
      baseAmount: baseAmount,
      totalAmount: baseAmount + totalTaxAmount,
      taxAmount: totalTaxAmount,
      taxLines: taxLines,
    );
  }

  /// Calculate taxes when price includes tax
  Future<TaxCalculationResult> _calculateTaxesIncluded(
    double totalAmount,
    List<AccountTaxTableData> taxes,
  ) async {
    // For price-included taxes, we need to work backwards
    // This is a simplified implementation - Odoo's actual logic is more complex
    
    double totalTaxRate = 0.0;
    List<TaxLineResult> taxLines = [];

    // Calculate total tax rate
    for (final tax in taxes) {
      if (tax.amount_type == 'percent') {
        totalTaxRate += tax.amount ?? 0.0;
      }
    }

    // Calculate base amount
    double baseAmount = totalAmount / (1 + totalTaxRate / 100);
    double totalTaxAmount = totalAmount - baseAmount;

    // Distribute tax amount proportionally
    for (final tax in taxes) {
      if (tax.amount_type == 'percent') {
        double taxRate = tax.amount ?? 0.0;
        double taxAmount = baseAmount * (taxRate / 100);
        
        taxLines.add(TaxLineResult(
          taxId: tax.id,
          taxName: tax.name,
          baseAmount: baseAmount,
          taxAmount: taxAmount,
          rate: taxRate,
          priceInclude: true,
        ));
      }
    }

    return TaxCalculationResult(
      baseAmount: baseAmount,
      totalAmount: totalAmount,
      taxAmount: totalTaxAmount,
      taxLines: taxLines,
    );
  }

  /// Calculate a single tax
  Future<TaxLineResult> _calculateSingleTax(
    AccountTaxTableData tax,
    double baseAmount,
  ) async {
    double taxAmount = 0.0;

    switch (tax.amount_type) {
      case 'percent':
        taxAmount = baseAmount * ((tax.amount ?? 0.0) / 100);
        break;
      case 'fixed':
        taxAmount = tax.amount ?? 0.0;
        break;
      case 'group':
        // For group taxes, we would need to calculate child taxes
        // This is a simplified implementation
        final childTaxIds = _parseChildTaxIds(tax.children_tax_ids);
        if (childTaxIds.isNotEmpty) {
          final childTaxes = await _database.accountTaxDao.getTaxesByIds(childTaxIds);
          for (final childTax in childTaxes) {
            final childResult = await _calculateSingleTax(childTax, baseAmount);
            taxAmount += childResult.taxAmount;
          }
        }
        break;
      case 'code':
        // Python code execution would go here
        // For now, we'll treat it as 0
        taxAmount = 0.0;
        break;
      default:
        taxAmount = 0.0;
    }

    return TaxLineResult(
      taxId: tax.id,
      taxName: tax.name,
      baseAmount: baseAmount,
      taxAmount: taxAmount,
      rate: tax.amount ?? 0.0,
      priceInclude: tax.price_include ?? false,
    );
  }

  /// Parse child tax IDs from JSON string
  List<int> _parseChildTaxIds(String? childTaxIdsJson) {
    if (childTaxIdsJson == null || childTaxIdsJson.isEmpty) return [];
    try {
      final List<dynamic> ids = json.decode(childTaxIdsJson);
      return ids.map((id) => id as int).toList();
    } catch (e) {
      return [];
    }
  }

  /// Calculate taxes for multiple lines (invoice level)
  Future<Map<String, dynamic>> calculateInvoiceTaxes({
    required List<AccountMoveLineTableData> lines,
  }) async {
    double totalUntaxed = 0.0;
    double totalTax = 0.0;
    double totalAmount = 0.0;
    Map<int, double> taxTotals = {}; // tax_id -> total_amount

    for (final line in lines) {
      if (line.is_deleted || line.isTaxLine()) continue;

      final taxIds = line.getTaxIds();
      final result = await calculateLineTaxes(
        priceUnit: line.price_unit ?? 0.0,
        quantity: line.quantity ?? 0.0,
        discount: line.discount ?? 0.0,
        taxIds: taxIds,
      );

      totalUntaxed += result.baseAmount;
      totalTax += result.taxAmount;

      // Accumulate tax totals by tax ID
      for (final taxLine in result.taxLines) {
        taxTotals[taxLine.taxId] = (taxTotals[taxLine.taxId] ?? 0.0) + taxLine.taxAmount;
      }
    }

    totalAmount = totalUntaxed + totalTax;

    return {
      'amount_untaxed': totalUntaxed,
      'amount_tax': totalTax,
      'amount_total': totalAmount,
      'tax_totals': taxTotals,
    };
  }

  /// Get default taxes for a product
  Future<List<int>> getProductDefaultTaxes({
    required int productId,
    required String taxType, // 'sale' or 'purchase'
  }) async {
    final product = await _database.productProductDao.getProductById(productId);
    if (product == null) return [];

    List<int> taxIds = [];
    if (taxType == 'sale') {
      taxIds = product.getCustomerTaxIds();
    } else if (taxType == 'purchase') {
      taxIds = product.getSupplierTaxIds();
    }

    // If product has no specific taxes, use company defaults
    if (taxIds.isEmpty) {
      final company = await _database.resCompanyDao.getCompanyById(product.company_id ?? 1);
      if (company != null) {
        if (taxType == 'sale' && company.account_sale_tax_id != null) {
          taxIds = [company.account_sale_tax_id!];
        } else if (taxType == 'purchase' && company.account_purchase_tax_id != null) {
          taxIds = [company.account_purchase_tax_id!];
        }
      }
    }

    return taxIds;
  }
}
