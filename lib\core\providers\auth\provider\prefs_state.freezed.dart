// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'prefs_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$PrefsState {
  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is PrefsState);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PrefsState()';
  }
}

/// @nodoc
class $PrefsStateCopyWith<$Res> {
  $PrefsStateCopyWith(PrefsState _, $Res Function(PrefsState) __);
}

/// @nodoc

class _PrefsStateInitial implements PrefsState {
  _PrefsStateInitial();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _PrefsStateInitial);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PrefsState.initial()';
  }
}

/// @nodoc

class _PrefsStateLoading implements PrefsState {
  _PrefsStateLoading();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _PrefsStateLoading);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'PrefsState.loading()';
  }
}

/// @nodoc

class _PrefsStateData implements PrefsState {
  _PrefsStateData({required this.workerProfile});

  final WorkerProfile workerProfile;

  /// Create a copy of PrefsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PrefsStateDataCopyWith<_PrefsStateData> get copyWith =>
      __$PrefsStateDataCopyWithImpl<_PrefsStateData>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PrefsStateData &&
            (identical(other.workerProfile, workerProfile) ||
                other.workerProfile == workerProfile));
  }

  @override
  int get hashCode => Object.hash(runtimeType, workerProfile);

  @override
  String toString() {
    return 'PrefsState.data(workerProfile: $workerProfile)';
  }
}

/// @nodoc
abstract mixin class _$PrefsStateDataCopyWith<$Res>
    implements $PrefsStateCopyWith<$Res> {
  factory _$PrefsStateDataCopyWith(
          _PrefsStateData value, $Res Function(_PrefsStateData) _then) =
      __$PrefsStateDataCopyWithImpl;
  @useResult
  $Res call({WorkerProfile workerProfile});

  $WorkerProfileCopyWith<$Res> get workerProfile;
}

/// @nodoc
class __$PrefsStateDataCopyWithImpl<$Res>
    implements _$PrefsStateDataCopyWith<$Res> {
  __$PrefsStateDataCopyWithImpl(this._self, this._then);

  final _PrefsStateData _self;
  final $Res Function(_PrefsStateData) _then;

  /// Create a copy of PrefsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? workerProfile = null,
  }) {
    return _then(_PrefsStateData(
      workerProfile: null == workerProfile
          ? _self.workerProfile
          : workerProfile // ignore: cast_nullable_to_non_nullable
              as WorkerProfile,
    ));
  }

  /// Create a copy of PrefsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $WorkerProfileCopyWith<$Res> get workerProfile {
    return $WorkerProfileCopyWith<$Res>(_self.workerProfile, (value) {
      return _then(_self.copyWith(workerProfile: value));
    });
  }
}

/// @nodoc

class _PrefsStateLoaded implements PrefsState {
  _PrefsStateLoaded([this.data = 0]);

  @JsonKey()
  final dynamic data;

  /// Create a copy of PrefsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PrefsStateLoadedCopyWith<_PrefsStateLoaded> get copyWith =>
      __$PrefsStateLoadedCopyWithImpl<_PrefsStateLoaded>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PrefsStateLoaded &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  @override
  String toString() {
    return 'PrefsState.loaded(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$PrefsStateLoadedCopyWith<$Res>
    implements $PrefsStateCopyWith<$Res> {
  factory _$PrefsStateLoadedCopyWith(
          _PrefsStateLoaded value, $Res Function(_PrefsStateLoaded) _then) =
      __$PrefsStateLoadedCopyWithImpl;
  @useResult
  $Res call({dynamic data});
}

/// @nodoc
class __$PrefsStateLoadedCopyWithImpl<$Res>
    implements _$PrefsStateLoadedCopyWith<$Res> {
  __$PrefsStateLoadedCopyWithImpl(this._self, this._then);

  final _PrefsStateLoaded _self;
  final $Res Function(_PrefsStateLoaded) _then;

  /// Create a copy of PrefsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = freezed,
  }) {
    return _then(_PrefsStateLoaded(
      freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc

class _PrefsStateError implements PrefsState {
  _PrefsStateError([this.error]);

  final String? error;

  /// Create a copy of PrefsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PrefsStateErrorCopyWith<_PrefsStateError> get copyWith =>
      __$PrefsStateErrorCopyWithImpl<_PrefsStateError>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PrefsStateError &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, error);

  @override
  String toString() {
    return 'PrefsState.error(error: $error)';
  }
}

/// @nodoc
abstract mixin class _$PrefsStateErrorCopyWith<$Res>
    implements $PrefsStateCopyWith<$Res> {
  factory _$PrefsStateErrorCopyWith(
          _PrefsStateError value, $Res Function(_PrefsStateError) _then) =
      __$PrefsStateErrorCopyWithImpl;
  @useResult
  $Res call({String? error});
}

/// @nodoc
class __$PrefsStateErrorCopyWithImpl<$Res>
    implements _$PrefsStateErrorCopyWith<$Res> {
  __$PrefsStateErrorCopyWithImpl(this._self, this._then);

  final _PrefsStateError _self;
  final $Res Function(_PrefsStateError) _then;

  /// Create a copy of PrefsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  $Res call({
    Object? error = freezed,
  }) {
    return _then(_PrefsStateError(
      freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
