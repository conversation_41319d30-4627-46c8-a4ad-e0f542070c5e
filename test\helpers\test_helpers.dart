import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:invoicer/core/db/drift/database.dart';
import 'package:invoicer/core/db/drift/database_service.dart';

/// Test helper utilities for Odoo sync tests
class TestHelpers {
  static late AppDatabase _testDatabase;
  
  /// Initialize test environment
  static Future<void> setUp() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    
    // Initialize SharedPreferences for testing
    SharedPreferences.setMockInitialValues({});
    
    // Create in-memory database for testing
    _testDatabase = AppDatabase(NativeDatabase.memory());
    
    // Override the database service to use our test database
    DatabaseService.overrideDatabase(_testDatabase);
  }
  
  /// Clean up test environment
  static Future<void> tearDown() async {
    await _testDatabase.close();
  }
  
  /// Get test database instance
  static AppDatabase get testDatabase => _testDatabase;
  
  /// Set up Odoo credentials for testing
  static Future<void> setUpOdooCredentials({
    String url = 'https://erp.kanjan.co.zw',
    String database = 'piggypro',
    String username = '<EMAIL>',
    String password = 'Secret1234',
    int userId = 2,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('odooUrl', url);
    await prefs.setString('database', database);
    await prefs.setString('username', username);
    await prefs.setString('password', password);
    await prefs.setString('userId', userId.toString());
  }
  
  /// Clear all test data from database
  static Future<void> clearTestData() async {
    await _testDatabase.delete(_testDatabase.resCompanyTable).go();
    await _testDatabase.delete(_testDatabase.resPartnerTable).go();
    await _testDatabase.delete(_testDatabase.productProductTable).go();
    await _testDatabase.delete(_testDatabase.productCategoryTable).go();
    await _testDatabase.delete(_testDatabase.accountMoveTable).go();
    await _testDatabase.delete(_testDatabase.accountMoveLineTable).go();
    await _testDatabase.delete(_testDatabase.resCurrencyTable).go();
    await _testDatabase.delete(_testDatabase.accountTaxTable).go();
  }
  
  /// Create test company data
  static Future<ResCompanyTableData> createTestCompany({
    String name = 'Test Company',
    int? universalId,
    bool isSynced = false,
  }) async {
    final companion = ResCompanyTableCompanion(
      name: Value(name),
      universal_id: Value(universalId),
      is_synced: Value(isSynced),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
    
    final id = await _testDatabase.resCompanyDao.insertOrUpdateCompany(companion);
    return await _testDatabase.resCompanyDao.getCompanyById(id) ?? 
           throw Exception('Failed to create test company');
  }
  
  /// Create test partner data
  static Future<ResPartnerTableData> createTestPartner({
    String name = 'Test Partner',
    String? email,
    int? companyId,
    int? universalId,
    bool isSynced = false,
  }) async {
    final companion = ResPartnerTableCompanion(
      name: Value(name),
      email: Value(email),
      company_id: Value(companyId),
      universal_id: Value(universalId),
      is_synced: Value(isSynced),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
    
    final id = await _testDatabase.resPartnerDao.insertOrUpdatePartner(companion);
    return await _testDatabase.resPartnerDao.getPartnerById(id) ?? 
           throw Exception('Failed to create test partner');
  }
  
  /// Create test product data
  static Future<ProductProductTableData> createTestProduct({
    String name = 'Test Product',
    double price = 100.0,
    int? companyId,
    int? categoryId,
    int? universalId,
    bool isSynced = false,
  }) async {
    final companion = ProductProductTableCompanion(
      name: Value(name),
      list_price: Value(price),
      company_id: Value(companyId),
      categ_id: Value(categoryId),
      universal_id: Value(universalId),
      is_synced: Value(isSynced),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
    
    final id = await _testDatabase.productProductDao.insertOrUpdateProduct(companion);
    return await _testDatabase.productProductDao.getProductById(id) ?? 
           throw Exception('Failed to create test product');
  }
  
  /// Create test currency data
  static Future<ResCurrencyTableData> createTestCurrency({
    String name = 'USD',
    String symbol = '\$',
    double rate = 1.0,
    int? universalId,
    bool isSynced = false,
  }) async {
    final companion = ResCurrencyTableCompanion(
      name: Value(name),
      symbol: Value(symbol),
      rate: Value(rate),
      universal_id: Value(universalId),
      is_synced: Value(isSynced),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
    
    final id = await _testDatabase.resCurrencyDao.insertOrUpdateCurrency(companion);
    return await _testDatabase.resCurrencyDao.getCurrencyById(id) ?? 
           throw Exception('Failed to create test currency');
  }
  
  /// Create test category data
  static Future<ProductCategoryTableData> createTestCategory({
    String name = 'Test Category',
    int? companyId,
    int? universalId,
    bool isSynced = false,
  }) async {
    final companion = ProductCategoryTableCompanion(
      name: Value(name),
      company_id: Value(companyId),
      universal_id: Value(universalId),
      is_synced: Value(isSynced),
      is_confirmed: const Value(true),
      is_deleted: const Value(false),
      version: const Value(1),
    );
    
    final id = await _testDatabase.productCategoryDao.insertOrUpdateCategory(companion);
    return await _testDatabase.productCategoryDao.getCategoryById(id) ?? 
           throw Exception('Failed to create test category');
  }
  
  /// Verify sync status of an entity
  static void verifySyncStatus(dynamic entity, bool expectedSynced, int? expectedUniversalId) {
    expect(entity.is_synced, equals(expectedSynced), 
           reason: 'Entity sync status should be $expectedSynced');
    expect(entity.universal_id, equals(expectedUniversalId), 
           reason: 'Entity universal_id should be $expectedUniversalId');
  }
  
  /// Wait for async operations to complete
  static Future<void> waitForAsync([Duration? duration]) async {
    await Future.delayed(duration ?? const Duration(milliseconds: 100));
  }
}
